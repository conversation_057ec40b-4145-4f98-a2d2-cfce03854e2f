站控系统扩容及抽油机智能管理平台

功

能

描

述

某某公司

目录

一、项目概况	1

1.1	项目背景	1

1.2	项目目标	1

二、技术路线	2

2.1	多源异构数据互联系统	3

2.2	基于微服务的云、边、端物联网信息管理平台	3

2.3	数字孪生场景应用系统	5

2.4	智能算法训练系统	5

2.5	LLM 智能体管理系统	6

2.6	相关应用业绩：	6

三、功能清单	7

四、解决方案	7

## 一、项目概况

- 项目背景

根据中石油华东设计院有限公司《油井在线检测系统规格书》对延长石油集团公司杏子川油田化子坪井区 26 万吨CO2 驱油及封存项目 391口油井井口进行在线检测改造，对其中软件监控管理平台进行功能设计。实现数据的存储、展示、报表统计、数据分析、异常报警、智能诊断、专家分析、权限管理等功能。

- 项目目标

在已实施硬件仪表环境的基础，对油井井口信息、二氧化碳计量撬和增压站计量撬进行统一管理，针对数据的存储、展示、报表统计、数据分析、异常报警、智能诊断、专家分析、权限管理等功能进行开发和设计，详细目标设计如下：

- 系统通用功能开发，包括用户权限管理、组织架构、系统运行指标监测、日志系统等基础模块。
- 边缘采集网关模块开发，实现多种不同物联网网络传输协议的管理、实现PLC、RTU、井口仪表、视频监控等物联网设备的实时接入。
- 设备管理模块开发，对现场设备进行物模型建模，将设备固有信息等静数据和实时采集的动数据进行统一管理，集中存储。包括油井仪表、二氧化碳计量撬和增压站计量撬中的各类仪表。
- 油井生产数据管理：包含油井信息录入、油井综合信息查看、油井仪表实时数据展示、功图平铺、功图对比。
- 油井生产分析模块：基于实时数据和历史趋势，对油井运行状态进行诊断和分析，实现各种小模型接入，包括功图诊断、功图量油、能耗管理、开关井统计、产量波动分析等。
- 组态管理模块：通过WEB组态技术，二氧化碳计量撬和增压站计量撬进行组态工艺流程页面的绘制及实时数据的接入。
- 报警管理模块：实现规则脚本的配置，可以通过多个不同仪表的数据按照与或非逻辑进行组合报警，降低误报率，
- 报表管理模块：配置报表模版、基于物联网数据自动生成日报、周报、月报并支持历史报表查询、对于物联网仪表没有覆盖到的数据支持人工补录。
- 设备运维管理：对设备的上线率、数据的准确率、异常处理的及时率进行综合管理，以数据大屏的方式进行查看，联动报警管理模型、生产数据管理模块、设备管理模块。支持运维队伍管理和工单闭环管理。
- AI模型管理模块：可以对机器学习小模型进行管理，支持模型的动态添加和推理部署。为油井生产分析模块注入智能。

## 二、技术路线

经过系统调研和项目分析，提出了 5+1+N 的设计理念，即 5 个基础微服务加一套标准化数据接口加N个场景应用，其中5个基础微服务包括：多源异构数据互联系统、物联网信息管理系统、数字孪生场景应用系统、智能算法训练系统和大语言模型智能体管理系统；标准化数据接口满足不同的用户展示需求并对不同的应用场景进行适配管理和无缝衔接，提升整个系统的通用性；有了多源异构数据互联系统、物联网信息管理系统、数字孪生场景应用系统、智能算法训练系统和大语言模型智能体管理系统以及标准化数据接口的加持，就可以以此为基础开发N个场景应用，通过微服务调度管理使整个系统有机地整合到一起，满足各功能设计需求和用户的使用需求。系统业务逻辑架构图如图 2-1所示。

<!-- image -->

图 2-1 系统业务逻辑架构图

- 多源异构数据互联系统

多源异构数据互联系统包括数据标准化、SDK与协议驱动管理、数据补传与一致性校验三个主要功能模块，实现全链路数据互通，支持与集团公司数据对接、与人员定位系统互联互通、与安眼工程智能化平台其他功能模块进行数据交互等10类业务系统对接。

- 基于微服务的云、边、端物联网信息管理平台

在石油生产场景下，物联网现场多个工控系统共存，系统之间彼此隔离，物理上距离较远，数据传输协议不统一，为了解决这些问题我们采用云边端的形式来部署物联网信息管理系统，边缘侧部署多个边缘采集网关，将现场设备通过分布式边缘网关服务集中管控起来，物联网信息管理平台数据仓储及算力服务器均部署在云端，通过与边缘侧网关进行数据通讯，采集全站的实时生产数据并下发执行指令。云、边、端系统物理拓扑结构如图 2-2所示。

<!-- image -->

图 2-2 云边端系统物理拓扑结构图

物联网信息管理系统整体功能架构如图 2-3所示。

<!-- image -->

图 2-3 物联网信息管理系统功能架构图

图 2-3中，主要模块的功能如下：

- 边缘网关模块：支持各类石油生产工业协议用于采集各类现场实时数据并提供直接控制功能；
- 物模型管理模块：将现场设备与仪表进行高度抽象，通过统一的属性、事件、方法管理与现场的真实仪表进行数据联动，从而实现设备的全生命周期管理，保证通用性与拓展性；
- 报警与通知模块：
- 场景联动：支持预设规则引擎（如"压力&gt;5MPa+流量异常=触发急停"），实现跨系统协同响应；
- 消息组管理：基于RBAC模型配置多级通知策略（短信/邮件/APP推送），支持按岗位/值班表轮询告警；

基于微服务架构开发的数字孪生系统，通过三维可视化引擎与实时数据融合，构建"空间-设备-业务"全要素联动体系。通过缓存、消息总线、全局任务管理等已有模块与其他服务进行数据通讯与同步。主要功能介绍如下：

- 三维场景管理功能：完成场站数字孪生模型的建模，实时对接承包商管理系统、作业许可系统及周边IPC摄像头，关键仪器仪表显示实时数据；
- 时空轨迹回放：进行多源定位融合使坐标系统一，将人员定位系统信息接入，进行时空对齐。基于NTP时间同步协议统一坐标系，支持72小时轨迹回溯与热力图分析；
- 人员聚集与电子围栏：四色密度分级算法（红/黄/蓝/绿），人员密度&gt;5人/㎡自动触发报警，并在数字孪生场景中高亮显示区域。支持GeoJSON电子围栏绘制，闯入检测响应时间≤200ms。
- 智能巡检：支持巡检路线标定，巡检轨迹回放，巡检记录上传、巡检报告生成。

采用容器化微服务架构构建的AI中台，提供算法全生命周期管理能力，支持石油行业特有场景下的模型迭代优化。提供数据集管理、数据标注、模型训练、模型部署与推理应用的基础平台，提供了各类算法学习和优化的能力，实现对算法进行集中管理，按照场景需要选择一个或多个算法绑定到对应的推理终端和数据源上。核心技术包括算法开发框架管理、数据集管理、图像识别模型训练、LSTM模型预测、模型管理与发布。

- LLM 智能体管理系统

基于大语言模型技术构建的智能中枢系统，通过知识图谱与RAG技术深度融合，实现安全生产知识智能化管理与分析。系统核心功能模块与实现方案如下：

- RAG语料管理模块，支持多模态知识处理，对于PDF/扫描件（OCR识别准确率≥99%，采用RAPTOR分层聚类算法构建行业知识图谱，采用BAAI/bge-m3多语言嵌入模型作为向量化引擎，以此提高召回准确性。
- MCP服务管理模块，支持对已建系统进行 MCP 服务改造，改造后可接入大语言模型，提供语料数据。对输出结果采用 RBAC 的形式进行权限管理，操作审计日志保留周期≥180天
- 智能体应用层：主要实现多智能体联动，使用 LangGraph 以图和工作流的形式让智能体之间协作工作，自主查找信息，最终获取希望的结果。内置了提示词优化模版库，内含大量石油行业场景提示词模板，可大幅提升各类场景信息检索的效率和准确性。
- 石油生产工艺快速仿真与智能管控系统入选2024年辽宁省重点研发项目；
- 2025年“金小海”油气生产智能助手获集团公司首届人工智能创新应用大赛场景设计组三等奖。

## 三、功能清单

针对本项目的需求，经系统分析后，得到如下的功能清单。

- 边缘采集网关模块

实现多种不同物联网网络传输协议的管理、实现PLC、RTU、井口仪表、视频监控等物联网设备的实时接入。

|        | 主要功能名称   | 功能描述                                                    |
|--------|----------|---------------------------------------------------------|
| 边缘采集网关 | 驱动管理     | 采集驱动和业务驱动导入导出功能。                                        |
| 边缘采集网关 | 驱动调试     | 测试每一个驱动的传输协议。                                           |
| 边缘采集网关 | 通道管理     | 一组数据流的最小管理单元，有采集驱动、业务驱动和通道内变量组成，通过采集驱动对变量赋值，通过业务驱动调用变量。 |
| 边缘采集网关 | 采集设备管理   | 采集设备（各类PLC、RTU、视频码流等物联网节点）接入参数配置。                       |
| 边缘采集网关 | 变量管理     | 每个通道内变量唯一。                                              |
| 边缘采集网关 | 业务设备管理   | 业务设备（各类数据服务，如MySQL、Redis、MQTT等）服务接口配置。                  |
| 边缘采集网关 | 日志管理     | 每一次业务驱动和采集驱动调用时的日志信息。                                   |

部分页面截图如下：

- 设备管理模块

对现场设备进行物模型建模，将设备固有信息等静数据和实时采集的动数据进行统一管理，集中存储。包括油井仪表、二氧化碳计量撬和增压站计量撬中的各类仪表。

|        | 主要功能名称   | 功能描述   |
|--------|----------|--------|
| 设备管理模块 | 物模型属性管理  |        |
| 设备管理模块 | 物模型产品分类  |        |
| 设备管理模块 | 物模型产品管理  |        |
| 设备管理模块 | 物模型设备分组  |        |
| 设备管理模块 | 物模型设备管理  |        |
| 设备管理模块 | 设备实时数据监测 |        |

部分页面截图如下：

- 油井生产数据管理

包含油井信息录入、油井综合信息查看、油井仪表实时数据展示、功图平铺、功图对比。

|          | 主要功能名称   | 功能描述   |
|----------|----------|--------|
| 油井生产数据管理 | 油井综合信息管理 |        |
| 油井生产数据管理 | 井筒仪表关联管理 |        |
| 油井生产数据管理 | 油井实时数据   |        |
| 油井生产数据管理 | 单井功图平铺   |        |
| 油井生产数据管理 | 日功图平铺    |        |
| 油井生产数据管理 | 功图对比     |        |

部分页面截图如下：

- 油井生产分析

基于实时数据和历史趋势，对油井运行状态进行诊断和分析，实现各种小模型接入，包括功图诊断、功图量油、能耗管理、开关井统计、产量波动分析等。

|          | 主要功能名称   | 功能描述   |
|----------|----------|--------|
| 油井生产分析模块 | 油井工况诊断   |        |
| 油井生产分析模块 | 油井工况分析   |        |
| 油井生产分析模块 | 油井功图计产   |        |
| 油井生产分析模块 | 抽油机能耗数据  |        |
| 油井生产分析模块 | 开关井统计    |        |
| 油井生产分析模块 | 产量波动分析   |        |

部分页面截图如下：

- 组态管理模块

通过WEB组态技术，二氧化碳计量撬和增压站计量撬进行组态工艺流程页面的绘制及实时数据的接入。

|        | 主要功能名称       | 功能描述   |
|--------|--------------|--------|
| 组态管理模块 | 组态页面管理       |        |
| 组态管理模块 | 组态图元管理       |        |
| 组态管理模块 | 组态图表管理       |        |
| 组态管理模块 | 物模型数据绑定      |        |
| 组态管理模块 | 二氧化碳计量撬工艺流程页 |        |
| 组态管理模块 | 增压站计量撬工艺流程页  |        |

部分页面截图如下：

- 报警管理模块

实现规则脚本的配置，可以通过多个不同仪表的数据按照与或非逻辑进行组合报警，降低误报率。并分级进行报警推送。

|        | 主要功能名称   | 功能描述   |
|--------|----------|--------|
| 报警管理模块 | 规则脚本管理   |        |
| 报警管理模块 | 场景联动管理   |        |
| 报警管理模块 | 报警配置     |        |
| 报警管理模块 | 报警日志     |        |
| 报警管理模块 | 报警处理     |        |

部分页面截图如下：

- 报表管理模块

配置报表模版、基于物联网数据自动生成日报、周报、月报并支持历史报表查询、对于物联网仪表没有覆盖到的数据支持人工补录。

|        | 主要功能名称   | 功能描述   |
|--------|----------|--------|
| 报表管理模块 | 报表模版管理   |        |
| 报表管理模块 | 单元格数据绑定  |        |
| 报表管理模块 | 查看报表     |        |
| 报表管理模块 | 报表数据人工录入 |        |
| 报表管理模块 | 报表审核     |        |
| 报表管理模块 | 历史报表查询   |        |

部分页面截图如下：

- 设备运维管理模块

对设备的上线率、数据的准确率、异常处理的及时率进行综合管理，以数据大屏的方式进行查看，联动报警管理模型、生产数据管理模块、设备管理模块。支持运维队伍管理和工单闭环管理。

|        | 主要功能名称   | 功能描述   |
|--------|----------|--------|
| 设备运维管理 | 设备运维指挥大屏 |        |
| 设备运维管理 | 设备上线率统计  |        |
| 设备运维管理 | 设备数据异常统计 |        |
| 设备运维管理 | 设备运维工单管理 |        |
| 设备运维管理 | 运维队伍管理   |        |

部分页面截图如下：

- AI算法管理模块

可以对机器学习小模型进行管理，支持模型的动态添加和推理部署。为油井生产分析模块注入智能。

|        | 主要功能名称     | 功能描述   |
|--------|------------|--------|
| AI算法管理 | 智能算法模型管理   |        |
| AI算法管理 | 模型部署发布     |        |
| AI算法管理 | 模型调用日志     |        |
| AI算法管理 | 算力资源占用情况统计 |        |

部分页面截图如下：

- 系统通用功能模块

包括用户权限管理、组织架构、系统运行指标监测、日志系统等基础模块。

|          | 主要功能名称   | 功能描述   |
|----------|----------|--------|
| 系统通用功能模块 | 用户管理     |        |
| 系统通用功能模块 | 组织管理     |        |
| 系统通用功能模块 | 岗位管理     |        |
| 系统通用功能模块 | 角色管理     |        |
| 系统通用功能模块 | 权限管理     |        |
| 系统通用功能模块 | 打印功能     |        |
| 系统通用功能模块 | 字典管理     |        |
| 系统通用功能模块 | 参数管理     |        |
| 系统通用功能模块 | 通知公告     |        |
| 系统通用功能模块 | 系统监控     |        |

部分页面截图如下：